<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'variant' => 'default', // default, elevated, bordered, ghost
    'padding' => 'default', // none, sm, default, lg, xl
    'hover' => false,
    'clickable' => false,
    'href' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'variant' => 'default', // default, elevated, bordered, ghost
    'padding' => 'default', // none, sm, default, lg, xl
    'hover' => false,
    'clickable' => false,
    'href' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$baseClasses = 'bg-card text-card-foreground rounded-xl transition-all duration-300';

$variantClasses = match($variant) {
    'elevated' => 'shadow-xl border border-border hover:shadow-2xl transform hover:-translate-y-1',
    'bordered' => 'border border-border hover:border-primary/30',
    'ghost' => 'bg-transparent hover:bg-card/50',
    default => 'shadow-lg border border-border hover:shadow-xl transform hover:-translate-y-0.5'
};

$paddingClasses = match($padding) {
    'none' => '',
    'sm' => 'p-4',
    'lg' => 'p-8',
    'xl' => 'p-12',
    default => 'p-6'
};

$hoverClasses = $hover ? 'hover:shadow-xl hover:-translate-y-1' : '';
$clickableClasses = $clickable ? 'cursor-pointer hover:shadow-xl hover:-translate-y-1' : '';

$classes = trim("{$baseClasses} {$variantClasses} {$paddingClasses} {$hoverClasses} {$clickableClasses}");
?>

<?php if($href): ?>
    <a href="<?php echo e($href); ?>" <?php echo e($attributes->merge(['class' => $classes])); ?>>
        <?php echo e($slot); ?>

    </a>
<?php else: ?>
    <div <?php echo e($attributes->merge(['class' => $classes])); ?>>
        <?php echo e($slot); ?>

    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\ngo-web\resources\views/components/card.blade.php ENDPATH**/ ?>