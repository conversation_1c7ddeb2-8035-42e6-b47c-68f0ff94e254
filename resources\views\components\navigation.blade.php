<nav id="navbar" class="fixed top-0 z-50 w-full transition-all duration-300 ease-in-out">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
            <!-- Logo -->
            <div class="flex-shrink-0 z-50">
                <a href="{{ route('home') }}" class="flex items-center space-x-3 group">
                    <div class="w-12 h-12 bg-gradient-to-br from-safety-yellow to-safety-orange rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:rotate-3 shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="hidden sm:block">
                        <span class="text-xl font-bold bg-gradient-to-r from-primary to-safety-green bg-clip-text text-transparent">VRSAF</span>
                        <p class="text-xs text-muted-foreground -mt-1 font-medium">Road Safety Foundation</p>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center justify-center flex-1">
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                        <span>Home</span>
                    </a>

                    <!-- Who We Are Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-2 {{ request()->routeIs('about.*') || request()->routeIs('members') ? 'active' : '' }}">
                            <span>Who We Are</span>
                            <svg class="w-4 h-4 transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 top-full mt-3 w-64 bg-white/98 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-3 group-hover:translate-y-0 z-50 ring-1 ring-black/5">
                            <div class="py-2">
                                <a href="{{ route('about.index') }}" class="dropdown-link">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">About Us</div>
                                            <div class="text-xs text-muted-foreground">Our mission & vision</div>
                                        </div>
                                    </div>
                                </a>
                                <a href="{{ route('members') }}" class="dropdown-link">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-safety-green/20 to-safety-green/10 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-safety-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Our Members</div>
                                            <div class="text-xs text-muted-foreground">Meet our team</div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- What We Do Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-2 {{ request()->routeIs('works.*') || request()->routeIs('training.*') || request()->routeIs('education.*') ? 'active' : '' }}">
                            <span>What We Do</span>
                            <svg class="w-4 h-4 transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 top-full mt-3 w-80 bg-white/98 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-3 group-hover:translate-y-0 z-50 ring-1 ring-black/5">
                            <div class="py-2">
                                <a href="{{ route('works.index') }}" class="dropdown-link">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-safety-orange/20 to-safety-orange/10 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-safety-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Our Works</div>
                                            <div class="text-xs text-muted-foreground">Projects & initiatives</div>
                                        </div>
                                    </div>
                                </a>
                                <a href="{{ route('training.index') }}" class="dropdown-link">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Capacity Building</div>
                                            <div class="text-xs text-muted-foreground">Training & development</div>
                                        </div>
                                    </div>
                                </a>
                                <a href="{{ route('education.index') }}" class="dropdown-link">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-safety-green/20 to-safety-green/10 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-safety-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Road Safety Education</div>
                                            <div class="text-xs text-muted-foreground">Awareness programs</div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Media Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-2 {{ request()->routeIs('media.*') || request()->routeIs('gallery.*') ? 'active' : '' }}">
                            <span>Media</span>
                            <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-3 w-56 bg-white/98 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-3 group-hover:translate-y-0 z-50 ring-1 ring-black/5">
                            <div class="py-1">
                                <a href="{{ route('media.coverage') }}" class="dropdown-link">Media Coverage</a>
                                <a href="{{ route('gallery.index') }}" class="dropdown-link">Gallery</a>
                            </div>
                        </div>
                    </div>
                    
                    <a href="{{ route('blog.index') }}" class="nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">
                        Blog
                    </a>
                    
                    <!-- Get Involved Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-2 {{ request()->routeIs('volunteer.*') || request()->routeIs('contact.*') ? 'active' : '' }}">
                            <span>Get Involved</span>
                            <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-3 w-56 bg-white/98 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-3 group-hover:translate-y-0 z-50 ring-1 ring-black/5">
                            <div class="py-1">
                                <a href="{{ route('volunteer.index') }}" class="dropdown-link">Become a Volunteer</a>
                                <a href="{{ route('contact.index') }}" class="dropdown-link">Contact Us</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden z-50">
                <button id="mobile-menu-button" type="button" class="relative inline-flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-foreground hover:bg-white/20 transition-all duration-300 group">
                    <span class="sr-only">Open main menu</span>
                    <div class="w-6 h-6 flex flex-col justify-center items-center">
                        <span id="hamburger-line-1" class="block w-6 h-0.5 bg-current transition-all duration-300 transform origin-center"></span>
                        <span id="hamburger-line-2" class="block w-6 h-0.5 bg-current mt-1.5 transition-all duration-300 transform origin-center"></span>
                        <span id="hamburger-line-3" class="block w-6 h-0.5 bg-current mt-1.5 transition-all duration-300 transform origin-center"></span>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Full-screen Mobile menu -->
    <div id="mobile-menu" class="fixed inset-0 z-40 lg:hidden hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-primary via-primary-hover to-safety-green">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                        </pattern>
                    </defs>
                    <rect width="100" height="100" fill="url(#grid)" />
                </svg>
            </div>
        </div>

        <div class="relative h-full flex flex-col justify-center px-8 py-20">
            <!-- Mobile Navigation Links -->
            <div class="space-y-8">
                <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                    <span class="mobile-nav-number">01</span>
                    <span class="mobile-nav-text">Home</span>
                </a>

                <!-- Who We Are Mobile -->
                <div class="space-y-4">
                    <div class="mobile-nav-section">Who We Are</div>
                    <a href="{{ route('about.index') }}" class="mobile-nav-link sub {{ request()->routeIs('about.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">02</span>
                        <span class="mobile-nav-text">About Us</span>
                    </a>
                    <a href="{{ route('members') }}" class="mobile-nav-link sub {{ request()->routeIs('members') ? 'active' : '' }}">
                        <span class="mobile-nav-number">03</span>
                        <span class="mobile-nav-text">Our Members</span>
                    </a>
                </div>

                <!-- What We Do Mobile -->
                <div class="space-y-4">
                    <div class="mobile-nav-section">What We Do</div>
                    <a href="{{ route('works.index') }}" class="mobile-nav-link sub {{ request()->routeIs('works.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">04</span>
                        <span class="mobile-nav-text">Our Works</span>
                    </a>
                    <a href="{{ route('training.index') }}" class="mobile-nav-link sub {{ request()->routeIs('training.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">05</span>
                        <span class="mobile-nav-text">Training</span>
                    </a>
                    <a href="{{ route('education.index') }}" class="mobile-nav-link sub {{ request()->routeIs('education.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">06</span>
                        <span class="mobile-nav-text">Education</span>
                    </a>
                </div>

                <!-- Media Mobile -->
                <div class="space-y-4">
                    <div class="mobile-nav-section">Media</div>
                    <a href="{{ route('media.coverage') }}" class="mobile-nav-link sub {{ request()->routeIs('media.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">07</span>
                        <span class="mobile-nav-text">Media Coverage</span>
                    </a>
                    <a href="{{ route('gallery.index') }}" class="mobile-nav-link sub {{ request()->routeIs('gallery.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">08</span>
                        <span class="mobile-nav-text">Gallery</span>
                    </a>
                </div>

                <a href="{{ route('blog.index') }}" class="mobile-nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">
                    <span class="mobile-nav-number">09</span>
                    <span class="mobile-nav-text">Blog</span>
                </a>

                <!-- Get Involved Mobile -->
                <div class="space-y-4">
                    <div class="mobile-nav-section">Get Involved</div>
                    <a href="{{ route('volunteer.index') }}" class="mobile-nav-link sub {{ request()->routeIs('volunteer.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">10</span>
                        <span class="mobile-nav-text">Volunteer</span>
                    </a>
                    <a href="{{ route('contact.index') }}" class="mobile-nav-link sub {{ request()->routeIs('contact.*') ? 'active' : '' }}">
                        <span class="mobile-nav-number">11</span>
                        <span class="mobile-nav-text">Contact</span>
                    </a>
                </div>
            </div>

            <!-- Mobile Menu Footer -->
            <div class="mt-16 pt-8 border-t border-white/20">
                <div class="text-white/80 text-sm">
                    <p class="font-medium">Vibrant Road Safety Awareness Foundation</p>
                    <p class="text-white/60 mt-1">Making roads safer for everyone</p>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
/* Navigation Styles */
.nav-link {
    @apply relative inline-block px-4 py-2.5 mx-1 text-sm font-semibold text-foreground/90 hover:text-primary transition-all duration-300 rounded-xl hover:bg-primary/10 backdrop-blur-sm border border-transparent hover:border-primary/20 whitespace-nowrap;
}

/* Ensure proper spacing for navigation container */
.hidden.lg\\:block > div {
    min-width: fit-content;
}

/* Fix dropdown button spacing */
.nav-link.flex {
    @apply gap-2;
}

/* Debug navigation layout */
@media (min-width: 1024px) {
    .hidden.lg\\:block {
        display: flex !important;
        align-items: center;
        justify-content: center;
        flex: 1;
    }

    .hidden.lg\\:block > div {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex-wrap: nowrap;
    }
}

.nav-link.active {
    @apply text-primary bg-primary/15 backdrop-blur-sm border-primary/30 shadow-sm;
}

.nav-link.active::after {
    content: '';
    @apply absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-0.5 bg-primary rounded-full;
}

.dropdown-link {
    @apply block px-4 py-3 text-sm text-gray-800 hover:text-primary hover:bg-primary/8 transition-all duration-300 rounded-xl mx-2 my-1 border border-transparent hover:border-primary/20;
}

.dropdown-link:hover .w-8 {
    @apply scale-110 rotate-3;
}

/* Mobile Navigation Styles */
.mobile-nav-link {
    @apply flex items-center space-x-4 py-4 text-white hover:text-safety-yellow transition-all duration-500 transform hover:translate-x-2;
}

.mobile-nav-link.sub {
    @apply ml-8 py-3;
}

.mobile-nav-link.active {
    @apply text-safety-yellow;
}

.mobile-nav-number {
    @apply text-xs font-mono text-white/50 w-8;
}

.mobile-nav-text {
    @apply text-2xl font-light tracking-wide;
}

.mobile-nav-link.sub .mobile-nav-text {
    @apply text-lg;
}

.mobile-nav-section {
    @apply text-xs font-semibold text-white/60 uppercase tracking-widest mb-2;
}

/* Navbar scroll effects */
.navbar-scrolled {
    @apply bg-white/95 backdrop-blur-md border-b border-white/20 shadow-lg;
}

.navbar-transparent {
    @apply bg-transparent;
}

/* Mobile menu animations */
#mobile-menu {
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

#mobile-menu:not(.hidden) {
    transform: translateX(0);
}

.mobile-menu-open #mobile-menu {
    transform: translateX(0);
}

/* Stagger animation for mobile menu items */
.mobile-nav-link {
    opacity: 0;
    transform: translateX(50px);
    animation: slideInLeft 0.6s ease-out forwards;
}

.mobile-nav-link:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav-link:nth-child(2) { animation-delay: 0.2s; }
.mobile-nav-link:nth-child(3) { animation-delay: 0.3s; }
.mobile-nav-link:nth-child(4) { animation-delay: 0.4s; }
.mobile-nav-link:nth-child(5) { animation-delay: 0.5s; }
.mobile-nav-link:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>


