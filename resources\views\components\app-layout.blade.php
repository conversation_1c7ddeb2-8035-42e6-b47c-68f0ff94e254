@props(['title' => 'Vibrant Road Safety Awareness Foundation', 'description' => 'Promoting road safety awareness and education for a safer tomorrow'])

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title }}</title>
    <meta name="description" content="{{ $description }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Additional Head Content -->
    @stack('head')
</head>
<body class="font-sans antialiased bg-background text-foreground min-h-screen">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Navigation -->
    <x-navigation />

    <!-- Main Content -->
    <main id="main-content" class="flex-1">
        {{ $slot }}
    </main>

    <!-- Footer -->
    <x-footer />

    <!-- Theme Switcher -->
    <x-theme-switcher />

    <!-- Scripts -->
    @stack('scripts')
    
    <!-- Mobile Menu Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const body = document.body;
            const hamburgerLines = [
                document.getElementById('hamburger-line-1'),
                document.getElementById('hamburger-line-2'),
                document.getElementById('hamburger-line-3')
            ];

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    const isHidden = mobileMenu.classList.contains('hidden');

                    if (isHidden) {
                        // Open menu
                        mobileMenu.classList.remove('hidden');
                        body.classList.add('mobile-menu-open');
                        body.style.overflow = 'hidden';

                        // Animate hamburger to X
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = 'rotate(45deg) translateY(8px)';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '0';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = 'rotate(-45deg) translateY(-8px)';
                    } else {
                        // Close menu
                        mobileMenu.classList.add('hidden');
                        body.classList.remove('mobile-menu-open');
                        body.style.overflow = '';

                        // Reset hamburger
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = '';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = '';
                    }
                });

                // Close mobile menu when clicking on links
                const mobileLinks = mobileMenu.querySelectorAll('a');
                mobileLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMenu.classList.add('hidden');
                        body.classList.remove('mobile-menu-open');
                        body.style.overflow = '';

                        // Reset hamburger
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = '';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = '';
                    });
                });

                // Close mobile menu on escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        body.classList.remove('mobile-menu-open');
                        body.style.overflow = '';

                        // Reset hamburger
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = '';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = '';
                    }
                });
            }
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // Navbar scroll effect
            const navbar = document.querySelector('nav');
            if (navbar) {
                let lastScrollY = window.scrollY;
                
                window.addEventListener('scroll', () => {
                    const currentScrollY = window.scrollY;
                    
                    if (currentScrollY > 100) {
                        navbar.classList.add('backdrop-blur-md', 'bg-background/80', 'shadow-sm');
                    } else {
                        navbar.classList.remove('backdrop-blur-md', 'bg-background/80', 'shadow-sm');
                    }
                    
                    lastScrollY = currentScrollY;
                });
            }
        });
    </script>
</body>
</html>
