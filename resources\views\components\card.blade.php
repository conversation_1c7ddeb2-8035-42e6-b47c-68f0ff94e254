@props([
    'variant' => 'default', // default, elevated, bordered, ghost
    'padding' => 'default', // none, sm, default, lg, xl
    'hover' => false,
    'clickable' => false,
    'href' => null
])

@php
$baseClasses = 'bg-card text-card-foreground rounded-xl transition-all duration-300';

$variantClasses = match($variant) {
    'elevated' => 'shadow-xl border border-border hover:shadow-2xl transform hover:-translate-y-1',
    'bordered' => 'border border-border hover:border-primary/30',
    'ghost' => 'bg-transparent hover:bg-card/50',
    default => 'shadow-lg border border-border hover:shadow-xl transform hover:-translate-y-0.5'
};

$paddingClasses = match($padding) {
    'none' => '',
    'sm' => 'p-4',
    'lg' => 'p-8',
    'xl' => 'p-12',
    default => 'p-6'
};

$hoverClasses = $hover ? 'hover:shadow-xl hover:-translate-y-1' : '';
$clickableClasses = $clickable ? 'cursor-pointer hover:shadow-xl hover:-translate-y-1' : '';

$classes = trim("{$baseClasses} {$variantClasses} {$paddingClasses} {$hoverClasses} {$clickableClasses}");
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <div {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </div>
@endif
