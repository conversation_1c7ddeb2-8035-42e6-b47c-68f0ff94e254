<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'variant' => 'primary', // primary, secondary, outline, ghost, destructive, success, warning
    'size' => 'default', // sm, default, lg, xl
    'href' => null,
    'type' => 'button',
    'disabled' => false,
    'loading' => false,
    'icon' => null,
    'iconPosition' => 'left' // left, right
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'variant' => 'primary', // primary, secondary, outline, ghost, destructive, success, warning
    'size' => 'default', // sm, default, lg, xl
    'href' => null,
    'type' => 'button',
    'disabled' => false,
    'loading' => false,
    'icon' => null,
    'iconPosition' => 'left' // left, right
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$baseClasses = 'inline-flex items-center justify-center rounded-xl font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95';

$variantClasses = match($variant) {
    'secondary' => 'bg-secondary text-secondary-foreground hover:bg-secondary-hover shadow-lg hover:shadow-xl',
    'outline' => 'border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-md hover:shadow-lg',
    'ghost' => 'hover:bg-accent hover:text-accent-foreground',
    'destructive' => 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg hover:shadow-xl',
    'success' => 'bg-success text-success-foreground hover:bg-success/90 shadow-lg hover:shadow-xl',
    'warning' => 'bg-warning text-warning-foreground hover:bg-warning/90 shadow-lg hover:shadow-xl',
    default => 'bg-primary text-primary-foreground hover:bg-primary-hover shadow-lg hover:shadow-xl'
};

$sizeClasses = match($size) {
    'sm' => 'h-9 px-3 text-sm',
    'lg' => 'h-11 px-8 text-base',
    'xl' => 'h-12 px-10 text-lg',
    default => 'h-10 px-4 py-2'
};

$classes = trim("{$baseClasses} {$variantClasses} {$sizeClasses}");

$iconClasses = match($size) {
    'sm' => 'w-4 h-4',
    'lg' => 'w-6 h-6',
    'xl' => 'w-7 h-7',
    default => 'w-5 h-5'
};
?>

<?php if($href): ?>
    <a href="<?php echo e($href); ?>" <?php echo e($attributes->merge(['class' => $classes])); ?>>
        <?php if($loading): ?>
            <svg class="animate-spin -ml-1 mr-3 <?php echo e($iconClasses); ?>" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        <?php elseif($icon && $iconPosition === 'left'): ?>
            <span class="mr-2 <?php echo e($iconClasses); ?>"><?php echo $icon; ?></span>
        <?php endif; ?>
        
        <?php echo e($slot); ?>

        
        <?php if($icon && $iconPosition === 'right'): ?>
            <span class="ml-2 <?php echo e($iconClasses); ?>"><?php echo $icon; ?></span>
        <?php endif; ?>
    </a>
<?php else: ?>
    <button 
        type="<?php echo e($type); ?>" 
        <?php echo e($disabled ? 'disabled' : ''); ?>

        <?php echo e($attributes->merge(['class' => $classes])); ?>

    >
        <?php if($loading): ?>
            <svg class="animate-spin -ml-1 mr-3 <?php echo e($iconClasses); ?>" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        <?php elseif($icon && $iconPosition === 'left'): ?>
            <span class="mr-2 <?php echo e($iconClasses); ?>"><?php echo $icon; ?></span>
        <?php endif; ?>
        
        <?php echo e($slot); ?>

        
        <?php if($icon && $iconPosition === 'right'): ?>
            <span class="ml-2 <?php echo e($iconClasses); ?>"><?php echo $icon; ?></span>
        <?php endif; ?>
    </button>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\ngo-web\resources\views/components/button.blade.php ENDPATH**/ ?>