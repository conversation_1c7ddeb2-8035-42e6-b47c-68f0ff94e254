<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['title' => 'Vibrant Road Safety Awareness Foundation', 'description' => 'Promoting road safety awareness and education for a safer tomorrow']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['title' => 'Vibrant Road Safety Awareness Foundation', 'description' => 'Promoting road safety awareness and education for a safer tomorrow']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($title); ?></title>
    <meta name="description" content="<?php echo e($description); ?>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <!-- Additional Head Content -->
    <?php echo $__env->yieldPushContent('head'); ?>
</head>
<body class="font-sans antialiased bg-background text-foreground min-h-screen">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Navigation -->
    <?php if (isset($component)) { $__componentOriginalf75d29720390c8e1fa3307604849a543 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf75d29720390c8e1fa3307604849a543 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navigation','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf75d29720390c8e1fa3307604849a543)): ?>
<?php $attributes = $__attributesOriginalf75d29720390c8e1fa3307604849a543; ?>
<?php unset($__attributesOriginalf75d29720390c8e1fa3307604849a543); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf75d29720390c8e1fa3307604849a543)): ?>
<?php $component = $__componentOriginalf75d29720390c8e1fa3307604849a543; ?>
<?php unset($__componentOriginalf75d29720390c8e1fa3307604849a543); ?>
<?php endif; ?>

    <!-- Main Content -->
    <main id="main-content" class="flex-1">
        <?php echo e($slot); ?>

    </main>

    <!-- Footer -->
    <?php if (isset($component)) { $__componentOriginal8a8716efb3c62a45938aca52e78e0322 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a8716efb3c62a45938aca52e78e0322 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a8716efb3c62a45938aca52e78e0322)): ?>
<?php $attributes = $__attributesOriginal8a8716efb3c62a45938aca52e78e0322; ?>
<?php unset($__attributesOriginal8a8716efb3c62a45938aca52e78e0322); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a8716efb3c62a45938aca52e78e0322)): ?>
<?php $component = $__componentOriginal8a8716efb3c62a45938aca52e78e0322; ?>
<?php unset($__componentOriginal8a8716efb3c62a45938aca52e78e0322); ?>
<?php endif; ?>

    <!-- Theme Switcher -->
    <?php if (isset($component)) { $__componentOriginal785aa89cb5e4b7a0bdd2387b4230b4ed = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal785aa89cb5e4b7a0bdd2387b4230b4ed = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.theme-switcher','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('theme-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal785aa89cb5e4b7a0bdd2387b4230b4ed)): ?>
<?php $attributes = $__attributesOriginal785aa89cb5e4b7a0bdd2387b4230b4ed; ?>
<?php unset($__attributesOriginal785aa89cb5e4b7a0bdd2387b4230b4ed); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal785aa89cb5e4b7a0bdd2387b4230b4ed)): ?>
<?php $component = $__componentOriginal785aa89cb5e4b7a0bdd2387b4230b4ed; ?>
<?php unset($__componentOriginal785aa89cb5e4b7a0bdd2387b4230b4ed); ?>
<?php endif; ?>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
    
    <!-- Mobile Menu Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Navigation script loaded');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const body = document.body;
            const hamburgerLines = [
                document.getElementById('hamburger-line-1'),
                document.getElementById('hamburger-line-2'),
                document.getElementById('hamburger-line-3')
            ];

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    const isHidden = mobileMenu.classList.contains('hidden');

                    if (isHidden) {
                        // Open menu
                        mobileMenu.classList.remove('hidden');
                        body.classList.add('mobile-menu-open');
                        body.style.overflow = 'hidden';

                        // Animate hamburger to X
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = 'rotate(45deg) translateY(8px)';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '0';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = 'rotate(-45deg) translateY(-8px)';
                    } else {
                        // Close menu
                        mobileMenu.classList.add('hidden');
                        body.classList.remove('mobile-menu-open');
                        body.style.overflow = '';

                        // Reset hamburger
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = '';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = '';
                    }
                });

                // Close mobile menu when clicking on links
                const mobileLinks = mobileMenu.querySelectorAll('a');
                mobileLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMenu.classList.add('hidden');
                        body.classList.remove('mobile-menu-open');
                        body.style.overflow = '';

                        // Reset hamburger
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = '';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = '';
                    });
                });

                // Close mobile menu on escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        body.classList.remove('mobile-menu-open');
                        body.style.overflow = '';

                        // Reset hamburger
                        if (hamburgerLines[0]) hamburgerLines[0].style.transform = '';
                        if (hamburgerLines[1]) hamburgerLines[1].style.opacity = '';
                        if (hamburgerLines[2]) hamburgerLines[2].style.transform = '';
                    }
                });
            }
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // Navbar scroll effect
            const navbar = document.querySelector('nav');
            if (navbar) {
                let lastScrollY = window.scrollY;

                window.addEventListener('scroll', () => {
                    const currentScrollY = window.scrollY;

                    if (currentScrollY > 100) {
                        navbar.classList.add('backdrop-blur-md', 'bg-background/80', 'shadow-sm');
                    } else {
                        navbar.classList.remove('backdrop-blur-md', 'bg-background/80', 'shadow-sm');
                    }

                    lastScrollY = currentScrollY;
                });
            }

            // Desktop Dropdown Functionality
            const dropdownContainers = document.querySelectorAll('.dropdown-container');
            console.log('Found dropdown containers:', dropdownContainers.length);

            dropdownContainers.forEach(container => {
                const trigger = container.querySelector('.dropdown-trigger');
                const menu = container.querySelector('.dropdown-menu');
                let timeoutId;

                if (trigger && menu) {
                    console.log('Setting up dropdown for:', container);

                    // Mouse enter
                    container.addEventListener('mouseenter', () => {
                        console.log('Mouse enter dropdown');
                        clearTimeout(timeoutId);
                        container.classList.add('active');
                        trigger.setAttribute('aria-expanded', 'true');
                    });

                    // Mouse leave
                    container.addEventListener('mouseleave', () => {
                        console.log('Mouse leave dropdown');
                        timeoutId = setTimeout(() => {
                            container.classList.remove('active');
                            trigger.setAttribute('aria-expanded', 'false');
                        }, 150);
                    });

                    // Keyboard navigation
                    trigger.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            container.classList.toggle('active');
                            trigger.setAttribute('aria-expanded',
                                container.classList.contains('active') ? 'true' : 'false'
                            );
                        } else if (e.key === 'Escape') {
                            container.classList.remove('active');
                            trigger.setAttribute('aria-expanded', 'false');
                        }
                    });
                }
            });

            // Mobile Dropdown Functionality
            const mobileDropdowns = document.querySelectorAll('.mobile-dropdown');
            console.log('Found mobile dropdowns:', mobileDropdowns.length);

            // Ensure all mobile dropdowns start closed
            mobileDropdowns.forEach(dropdown => {
                dropdown.classList.remove('dropdown-open');
                dropdown.classList.remove('active');
                console.log('Closed mobile dropdown:', dropdown);
            });

            mobileDropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('.mobile-nav-dropdown-trigger');
                const content = dropdown.querySelector('.mobile-dropdown-content');

                if (trigger && content) {
                    trigger.addEventListener('click', (e) => {
                        e.preventDefault();

                        // Close other dropdowns
                        mobileDropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('dropdown-open');
                            }
                        });

                        // Toggle current dropdown
                        dropdown.classList.toggle('dropdown-open');
                    });
                }
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.dropdown-container')) {
                    dropdownContainers.forEach(container => {
                        container.classList.remove('active');
                        const trigger = container.querySelector('.dropdown-trigger');
                        if (trigger) trigger.setAttribute('aria-expanded', 'false');
                    });
                }
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\ngo-web\resources\views/components/app-layout.blade.php ENDPATH**/ ?>